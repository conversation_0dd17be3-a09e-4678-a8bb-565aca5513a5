import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AulasService } from '@base-core/agenda/aulas.service';
import { Periodo } from '@base-core/agenda/periodo.model';
import { Config } from '@base-core/empresa/config.model';
import { Empresa } from '@base-core/empresa/empresa.model';
import { EmpresaService } from '@base-core/empresa/empresa.service';
import { AgendaDisponibilidade } from '@base-core/locacao-ambiente/agenda-disponibilidade.model';
import { LocacaoAmbienteService } from '@base-core/locacao-ambiente/locacao-ambiente.service';
import { PeriodoLocacao } from '@base-core/locacao-ambiente/periodo-locacao.model';
import { NegociacaoService } from '@base-core/negociacao/negociacao.service';
import { ProdutoService } from '@base-core/produto/produto.service';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';

@Component({
  selector: 'pacto-agenda-locacao-ambiente',
  templateUrl: './agenda-locacao-ambiente.component.html',
  styleUrls: ['./agenda-locacao-ambiente.component.scss']
})
export class AgendaLocacaoAmbienteComponent implements OnInit {

  periodosLocacao: Array<PeriodoLocacao>;
  tipoSelecionado: string | null = null;

  constructor(private empresaService: EmpresaService,
              private produtoService: ProdutoService,
              private negociacaoService: NegociacaoService,
              private route: ActivatedRoute,
              private translateService: TranslateService,
              private aulasService: AulasService,
              private locacaoAmbienteService: LocacaoAmbienteService,
  ) {
    this.route.queryParams.subscribe(params => {
      if(params['k'] && params['un']) {
        this.negociacaoService.chave = params['k'];
        this.negociacaoService.codunidade = params['un'];
        window.localStorage.setItem('chave', params['k']);
        window.localStorage.setItem('unidade', params['un']);
      }
    })
  }

  ngOnInit() {
    this.loadUnidade();
    this.loadLocacoesDisponiveis();
  }

  getConfig(): Config {
    return this.empresaService.config;
  }

  getUnidadeSelecionada(): Empresa {
    return this.empresaService.unidadeSelecionada;
  }

  loadUnidade(): void {
    if (!this.empresaService.unidadeSelecionada) {
      this.empresaService.obterEmpresa(
        this.negociacaoService.chave,
        this.negociacaoService.codunidade)
        .subscribe(data => this.empresaService.unidadeSelecionada = data);

      this.empresaService.obterConfigs(this.negociacaoService.chave,
        this.negociacaoService.codunidade).subscribe(data => this.empresaService.config = data);
    }
  }

  loadLocacoesDisponiveis(): void {
    this.locacaoAmbienteService.dia = this.aulasService.dia;
    this.locacaoAmbienteService.obterLocacoesDisponiveis(this.negociacaoService.codunidade, this.negociacaoService.chave).subscribe(result => {
      this.periodosLocacao = this.processarLocacoesDisponiveisPorPeriodo(result);
      console.log(this.periodosLocacao);
    })
  }

  get dia() {
    return this.aulasService.dia;
  }

  private processarLocacoesDisponiveisPorPeriodo(lstAgendaDisponibilidade: Array<AgendaDisponibilidade>): Array<PeriodoLocacao> {
    const manha: AgendaDisponibilidade[] = [];
    const tarde: AgendaDisponibilidade[] = [];
    const noite: AgendaDisponibilidade[] = [];

    lstAgendaDisponibilidade.forEach(it => {
      const horaItem = moment(it.horarioInicial, 'HH:mm');

      if(horaItem.isBefore(moment('12:00', 'HH:mm'))) {
        manha.push(it);
      }else if(horaItem.isBefore(moment('18:00', 'HH:mm'))) {
        tarde.push(it);
      }else{
        noite.push(it);
      }
    });

    const ordenarPorHorarioInicial = (a: AgendaDisponibilidade, b: AgendaDisponibilidade) => {
      const [h1, m1] = a.horarioInicial.split(':').map(Number);
      const [h2, m2] = b.horarioInicial.split(':').map(Number);
      return h1 !== h2 ? h1 - h2 : m1 - m2;
    };

    manha.sort(ordenarPorHorarioInicial);
    tarde.sort(ordenarPorHorarioInicial);
    noite.sort(ordenarPorHorarioInicial);

    const periodos: PeriodoLocacao[] = [];

    if(manha.length > 0) {
      periodos.push(new PeriodoLocacao('Manhã', manha));
    }

    if(tarde.length > 0) {
      periodos.push(new PeriodoLocacao('Tarde', tarde));
    }

    if(noite.length > 0) {
      periodos.push(new PeriodoLocacao('Noite', noite));
    }

    return periodos;

  }

  get locacoesSelecionadas() {
    return this.locacaoAmbienteService.locacoesSelecionadas.length;
  }


  handleSlotSelecionado(slot: AgendaDisponibilidade) {
    if(this.locacaoAmbienteService.locacoesSelecionadas.length === 0) {
      this.tipoSelecionado = null;
    }else{
      this.tipoSelecionado = slot.tipoHorario;
    }
  }

  handleRemoverLocacao(removeuTodos: boolean) {
    if(removeuTodos || (!removeuTodos && this.locacaoAmbienteService.locacoesSelecionadas.length === 0)) {
      this.tipoSelecionado = null
    }
  }

}
