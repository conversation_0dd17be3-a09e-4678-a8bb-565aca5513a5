import { Input, OnInit} from '@angular/core';
import {Form, FormGroup} from '@angular/forms';
import Swal from 'sweetalert2';
import {Venda} from '@base-core/negociacao/venda.model';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {NegociacaoService} from '@base-core/negociacao/negociacao.service';
import {Aluno} from '@base-core/aluno/aluno.model';
import {AlunoService} from '@base-core/aluno/aluno.service';
import {PlanoService} from '@base-core/plano/plano.service';
import {ProdutoService} from '@base-core/produto/produto.service';
import {FormaPagamento} from './enum-forma-pagamento';
import {ActivatedRoute} from '@angular/router';
import {TranslateService} from '@ngx-translate/core';
import { OrigemSistemaEnum } from '@base-core/negociacao/enum-origem-sistema';

declare var window;

export class InfoPagamentos implements OnInit {
  @Input() formGroup: FormGroup;
  processando = false;
  empresaService: EmpresaService;
  negociacaoService: NegociacaoService;
  alunoService: AlunoService;
  planoService: PlanoService;
  produtoService: ProdutoService;
  formaPagamento: number;
  route: ActivatedRoute;


  constructor(
      formaPagamento: number,
      private translateService: TranslateService
  ) {
    this.formaPagamento = formaPagamento;
  }

  ngOnInit() {

  }

  getCobrancaJson(): Venda {
    if (this.formaPagamento === FormaPagamento.CARTAO) {
      const nrcartao = this.formGroup.get('nrcartao').value ? this.formGroup.get('nrcartao').value.replace(/ /g, '') : '';
      return new Venda(this.empresaService.unidadeSelecionada.codigo,
        0,
        '',
        '',
        '',
        '',
        '',
        this.formGroup.get('nomecartao').value,
        nrcartao,
        this.formGroup.get('validade').value ? this.formGroup.get('validade').value.replace('/', '/20') : '',
        this.formGroup.get('cvv').value,
        '', '', '', '', '', '', null, (this.formGroup.get('parcelasCartao') ? this.formGroup.get('parcelasCartao').value : null),
        '', (this.formGroup.get('cpftitularcard') ? this.formGroup.get('cpftitularcard').value : null),
        (this.formGroup.get('vencimentoFatura') ? this.formGroup.get('vencimentoFatura').value : 0), [],
        this.negociacaoService.cobrarParcelasEmAberto, null, null, null, null, null, [], false, null,
        this.negociacaoService.origemCobranca,
        this.negociacaoService.cobrancaAntecipada,
        this.negociacaoService.responsavel,
        this.negociacaoService.token,
        1,
        '',
        this.negociacaoService.codigoEvento,
        this.negociacaoService.usuarioResponsavel,
        this.negociacaoService.codigoRegistroAcessoPagina,
        '',
        (this.formGroup.get('tipoCredito') ? this.formGroup.get('tipoCredito').value : null),
        '',
        '',
        '',
        '',
        this.negociacaoService.lancarContratoConcomitanteAoInvesDeRenovar,
        this.negociacaoService.pactoPayComunicacao,
        '', null, null, null, this.negociacaoService.parcelasSelecionadas, this.negociacaoService.todasEmAberto,
        OrigemSistemaEnum.VENDAS_ONLINE, null, false
      );
    } else if (this.formaPagamento === FormaPagamento.PIX) {
      return new Venda(this.empresaService.unidadeSelecionada.codigo,
        0,
        '',
        '',
        '',
        '',
        '',
        '',
        null,
        '',
        '',
        '', '', '', '', '', '', null, null,
        '', null,
        0, [],
        this.negociacaoService.cobrarParcelasEmAberto, null, null, null, null, null, [], false, null,
        this.negociacaoService.origemCobranca, this.negociacaoService.cobrancaAntecipada, this.negociacaoService.responsavel,
        this.negociacaoService.token,
        1, '',
        this.negociacaoService.codigoEvento,
        this.negociacaoService.usuarioResponsavel,
        this.negociacaoService.codigoRegistroAcessoPagina,
        '',
        null, '',
        '',
        '',
        '',
        this.negociacaoService.lancarContratoConcomitanteAoInvesDeRenovar,
        this.negociacaoService.pactoPayComunicacao,
        '', null, null, null, this.negociacaoService.parcelasSelecionadas, this.negociacaoService.todasEmAberto,
        OrigemSistemaEnum.VENDAS_ONLINE, null, false
      );
    } else {
      return new Venda(this.empresaService.unidadeSelecionada.codigo,
        0,
        '',
        '',
        '',
        '',
        '',
        '',
        null,
        '',
        '',
        '', '', '', '', '', '', null, null,
        '', null,
        0, [],
        this.negociacaoService.cobrarParcelasEmAberto, null, null, null, null, null, [], false, null,
        this.negociacaoService.origemCobranca, this.negociacaoService.cobrancaAntecipada, this.negociacaoService.responsavel,
        this.negociacaoService.token, 1, '', this.negociacaoService.codigoEvento,
        this.negociacaoService.usuarioResponsavel, this.negociacaoService.codigoRegistroAcessoPagina,
        '',
        null, '',
        '', '', '', this.negociacaoService.lancarContratoConcomitanteAoInvesDeRenovar,
        this.negociacaoService.pactoPayComunicacao,
        '', null, null, null, this.negociacaoService.parcelasSelecionadas, this.negociacaoService.todasEmAberto,
        OrigemSistemaEnum.VENDAS_ONLINE, null, false
      );
    }
  }

  alunoSelecionado(): Aluno {
    return this.alunoService.alunoSelecionado ? this.alunoService.alunoSelecionado : new Aluno(null, '', '', '',
      '', 0.0, '', false, 1, 0.0, null, '',
      '', '', '');
  }

  naoAprovada(msg: string): void {
    msg = this.removerTagsHtml(msg);

    this.processando = false;
    let title = this.alunoSelecionado().valorCobrar === 0.0 ? this.translateService.instant('checkout.cartao-nao-foi-adicionado') : this.translateService.instant('checkout.pagamento-nao-confirmado');
    let text = this.alunoSelecionado().valorCobrar === 0.0 ? msg : this.translateService.instant('checkout.nao-foi-confirmado-pagamento-revise-suas-informacoes') + ' ' + msg;

    if (this.formaPagamento === FormaPagamento.PIX) {
      if (this.empresaService && this.empresaService.config && this.empresaService.config.primeiraCobrancaPixEGuardarCartao) {
        title = this.translateService.instant('Não foi possível realizar a venda!');
        text = msg;
      } else {
        title = this.translateService.instant('checkout.pix-nao-pode-ser-gerado');
        text = msg;
      }
    } else if (this.formaPagamento === FormaPagamento.BOLETO) {
      title = this.translateService.instant('checkout.boleto-nao-pode-ser-gerado');
      text = msg;
    }
    if (text.includes('ERRO: Você comprou')) {
      text = text.replace('ERRO: ', '');
      Swal.fire({
        type: 'info',
        title: 'Atenção',
        text: text,
        allowOutsideClick: false,
        showConfirmButton: true
      });
    } else if (text.includes('ID_')) {
      text = text.replace('ERRO: ', '');
      Swal.fire({
        type: 'warning',
        title: 'Não foi possível realizar a venda',
        text: text,
        allowOutsideClick: false,
        showConfirmButton: true
      });
    } else {
      Swal.fire({
        type: 'error',
        title: title,
        text: text,
        allowOutsideClick: false,
        showConfirmButton: true
      });
    }
  }

  urlRedirect(): string {
    if (this.empresaService.config === undefined ||
      ((!(this.empresaService.config === undefined)) &&
        this.empresaService.config != null &&
        ((this.empresaService.config.url != null && this.empresaService.config.url === '') ||
          this.empresaService.config.ativarLinksGooglePlayEAppleStore))) {
      var url = window.location.href;
      if (window.location.href.includes('pagamento')) {
        url = window.location.href.replace('pagamento', 'pospagamento');
        url = url + '?un=' + this.negociacaoService.codunidade + '&k=' + this.negociacaoService.chave;
      } else if (window.location.href.includes('checkout')) {
        url = url.substring(0, url.indexOf('checkout'));
        url = url + 'pospagamento?un=' + this.negociacaoService.codunidade + '&k=' + this.negociacaoService.chave;
      }
      return url;
    }
    return this.empresaService.config.url;
  }

  redirectPosVenda(): void {
    Swal.fire({
      onOpen: function () {
        Swal.showLoading();
      }
    });
    window.location.href = this.urlRedirect();
  }

  removerTagsHtml(msg: string): string {
    // Regex para remover as tags <br>, <br/>, <b>, e </b>
    // Se no futuro for necessário remover outras tags, basta adicionar na expressão regular
    return msg.replace(/<\/?b>|<br\s*\/?>/gi, '');
  }

}
