import { Component, OnInit } from '@angular/core';
import {NegociacaoService} from '@base-core/negociacao/negociacao.service';
import {ActivatedRoute} from '@angular/router';
import {ProdutoService} from '@base-core/produto/produto.service';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {AulasService} from '@base-core/agenda/aulas.service';
import {Periodo} from '@base-core/agenda/periodo.model';
import {PlanoService} from '@base-core/plano/plano.service';
import {FormGroup} from '@angular/forms';
import Swal from 'sweetalert2';
import {Plano} from '@base-core/plano/plano.model';
import {Venda} from '@base-core/negociacao/venda.model';
import {Config} from '@base-core/empresa/config.model';
import {VendaProduto} from '@base-core/produto/produto.model';
import {Empresa} from '@base-core/empresa/empresa.model';
import {TranslateService} from '@ngx-translate/core';
import {ClienteService} from '@base-core/cliente/cliente.service';
import {Cliente} from '@base-core/cliente/cliente.model';
import {ReCaptchaV3Service} from 'ng-recaptcha';
import {LocalizationService} from '@base-core/service/localization.service';
import { OrigemSistemaEnum } from '@base-core/negociacao/enum-origem-sistema';

@Component({
  selector: 'pacto-aulas',
  templateUrl: './aulas.component.html',
  styleUrls: ['./aulas.component.scss']
})
export class AulasComponent implements OnInit {

  periodos: Array<Periodo>;
  formGroup: FormGroup = new FormGroup({});
  leitura: boolean = false;
  linkVisitante: boolean = false;

  constructor(private negociacaoService: NegociacaoService,
              private route: ActivatedRoute,
              private aulasService: AulasService,
              private produtoService: ProdutoService,
              private planoService: PlanoService,
              private empresaService: EmpresaService,
              private clienteService: ClienteService,
              private recaptchaV3Service: ReCaptchaV3Service,
              private localizationService: LocalizationService,
              private translateService: TranslateService) {
    this.route.queryParams.subscribe(params => {
      if (params['k'] && params['un']) {
      this.negociacaoService.chave = params['k'];
      this.negociacaoService.codunidade = params['un'];
      window.localStorage.setItem('chave', params['k']);
      window.localStorage.setItem('unidade', params['un']);
      if(params['le']) {
        this.leitura = true;
        this.loadPeriodos();
      }
    } else {
      this.negociacaoService.chave = window.localStorage.getItem('chave');
      this.negociacaoService.codunidade = window.localStorage.getItem('unidade');
    }
      if (params['pl']) {
        window.localStorage.setItem('plano', params['pl']);
        this.planoService.obterPlano(this.negociacaoService.chave, this.negociacaoService.codunidade, params['pl'])
          .subscribe(data => {
            this.planoService.planoSelecionado = data;
            this.setarPlano(this.getVendaJson());
          });
      }
      if (params['pr']) {
        this.produtoService.obterProduto(this.negociacaoService.chave, this.negociacaoService.codunidade, params['pr'])
          .subscribe(data => {
            this.produtoService.adicionarProduto(data, 0);
            window.localStorage.setItem('produtos', JSON.stringify(this.produtoService.produtosSelecionados));
            this.loadPeriodos();
          });
      }
      if (params['us']) {
        this.negociacaoService.usuarioResponsavel = params['us'];
      }
      if (params['linkVisitante']) {
        this.linkVisitante = params['linkVisitante'];
        this.clienteService.chave = params['k'];
      }
  });
  }
  msgPlanoIndisponivel(): void {
    Swal.fire({
      type: 'error',
      title: 'Plano indisponível',
      text: 'Este plano não está mais disponível para venda,\n' +
        'por favor selecione outro plano.',
      showConfirmButton: true
    });
  }


  setarPlano(venda): void {
    console.log(venda);
    if (venda.plano === null) {
      this.msgPlanoIndisponivel();
    } else {
      this.planoService.planoSelecionado.codigo = venda.plano;
    }
  }
  ngOnInit() {
    this.loadUnidade();
    this.loadPeriodos();
    localStorage.removeItem('limparDadosCheckout');
  }
  scroll(el: HTMLElement) {
    el.scrollIntoView({behavior: 'smooth'});
  }
  get aulasSelecionadas() {
    return this.aulasService.aulasSelecionadas.length;
  }
  get aulas() {
    return this.aulasService.aulasSelecionadas;
  }
  getPlanoSelecionado(): Plano {
    return this.planoService.planoSelecionado;
  }
  getProdutosSelecionados(): Array<VendaProduto> {
    return this.produtoService.produtosSelecionados;
  }
  get dia() {
    return this.aulasService.dia;
  }

  loadPeriodos(): void {
    this.aulasService.obterPeriodos(
      this.negociacaoService.chave, this.negociacaoService.codunidade,
      this.getPlanoSelecionado() ? this.getPlanoSelecionado().codigo : 0,
      this.getProdutosSelecionados().length > 0 ? this.getProdutosSelecionados()[0].produto : 0,
      this.linkVisitante,
      )
      .subscribe(data => {
        this.periodos = data;
      });
  }

  loadUnidade(): void {
    if (!this.empresaService.unidadeSelecionada) {
      this.empresaService.obterEmpresa(
        this.negociacaoService.chave,
        this.negociacaoService.codunidade)
        .subscribe(data => this.empresaService.unidadeSelecionada = data);

      this.empresaService.obterConfigs(this.negociacaoService.chave,
        this.negociacaoService.codunidade).subscribe(data => this.empresaService.config = data);
    }
  }

  getVendaJson(): Venda {
    return new Venda(this.empresaService.unidadeSelecionada.codigo,
      (this.planoService.planoSelecionado ? this.planoService.planoSelecionado.codigo : 0),
      (this.formGroup.get('nome') ? this.formGroup.get('nome').value : null),
      (this.formGroup.get('cpf') ? this.formGroup.get('cpf').value : null),
      (this.formGroup.get('sexo') ? this.formGroup.get('sexo').value : null),
      (this.formGroup.get('dataNascimento') ? this.formGroup.get('dataNascimento').value : ''),
      this.formGroup.get('email') ? this.formGroup.get('email').value : null,
      this.formGroup.get('nomecartao') ? this.formGroup.get('nomecartao').value : null,
      this.formGroup.get('nrcartao') ? this.formGroup.get('nrcartao').value.replace(/ /g, '') : '',
      this.formGroup.get('validade') ? this.formGroup.get('validade').value.replace('/', '/20') : '',
      this.formGroup.get('cvv') ? this.formGroup.get('cvv').value : null,
      (this.formGroup.get('telefone') ? this.formGroup.get('telefone').value : null),
      (this.formGroup.get('endereco') ? this.formGroup.get('endereco').value : null),
      (this.formGroup.get('numero') ? this.formGroup.get('numero').value : null),
      (this.formGroup.get('bairro') ? this.formGroup.get('bairro').value : null),
      (this.formGroup.get('complemento') ? this.formGroup.get('complemento').value : null),
      (this.formGroup.get('cep') ? this.formGroup.get('cep').value : null),
      (this.formGroup.get('diavencimento') ? this.formGroup.get('diavencimento').value : null),
      (this.formGroup.get('parcelasCartao') ? this.formGroup.get('parcelasCartao').value : null),
      (this.formGroup.get('cupomdesconto') ? this.formGroup.get('cupomdesconto').value : ''),
      (this.formGroup.get('cpftitularcard') ? this.formGroup.get('cpftitularcard').value : null),
      (this.formGroup.get('vencimentoFatura') ? this.formGroup.get('vencimentoFatura').value : 0),
      [], this.negociacaoService.cobrarParcelasEmAberto,
      (this.formGroup.get('dataInicioContrato') ? this.formGroup.get('dataInicioContrato').value : null),
      (this.formGroup.get('responsavelPai') ? this.formGroup.get('responsavelPai').value : null),
      (this.formGroup.get('responsavelMae') ? this.formGroup.get('responsavelMae').value : null),
      (this.formGroup.get('cpfMae') ? this.formGroup.get('cpfMae').value : null),
      (this.formGroup.get('cpfPai') ? this.formGroup.get('cpfPai').value : null),
      [], false, (this.negociacaoService.categoriaPlano ? this.negociacaoService.categoriaPlano : null),
      this.negociacaoService.origemCobranca, this.negociacaoService.cobrancaAntecipada, this.negociacaoService.responsavel,
      this.negociacaoService.token, this.planoService.vezesEscolhidasParcelarTaxaMatricula,
      (this.formGroup.get('rg') ? this.formGroup.get('rg').value : null), this.negociacaoService.codigoEvento,
      this.negociacaoService.usuarioResponsavel, this.negociacaoService.codigoRegistroAcessoPagina,
      '',
      (this.formGroup.get('tipoCredito') ? this.formGroup.get('tipoCredito').value : null), '',
      (this.formGroup.get('cnpj') ? this.formGroup.get('cnpj').value : null),
      (this.formGroup.get('nomeResponsavelEmpresa') ? this.formGroup.get('nomeResponsavelEmpresa').value : null),
      (this.formGroup.get('cpfResponsavelEmpresa') ? this.formGroup.get('cpfResponsavelEmpresa').value : null),
      this.negociacaoService.lancarContratoConcomitanteAoInvesDeRenovar,
      this.negociacaoService.pactoPayComunicacao,
      '', null, null, null, null, false,
      OrigemSistemaEnum.VENDAS_ONLINE,
      this.formGroup.get("dataUtilizacao") ? this.formGroup.get("dataUtilizacao").value : null,
      false
    );
  }

  getConfig(): Config {
    return this.empresaService.config;
  }

  getUnidadeSelecionada(): Empresa {
    return this.empresaService.unidadeSelecionada;
  }

  finalizarCadastro() {
    Swal.fire({
      title: 'Processando...',
      onOpen: function () {
        Swal.showLoading();
      }
    });
    this.recaptchaV3Service.execute('importantAction')
      .subscribe((token) => {
          const dto = this.getCadastroCleinteJson(JSON.parse(window.localStorage.getItem('cliente')));
          this.clienteService.gravarCadastro(dto, token).subscribe((result) => {
            if (result['return'] === 'ALUNO CADASTRADO COM SUCESSO' || result['return'] === 'FREEPASS LANÇADO COM SUCESSO') {
              this.concluir(result['voucher']);
            } else {
              this.RealizaodAprovada(result['return']);
            }
          });
        }
      );
  }

  getCadastroCleinteJson(json): Cliente {
    return new Cliente(
      json.unidade,
      json.nome,
      (json.cpf ? json.cpf : null),
      (json.responsavelPai ? json.responsavelPai : null),
      (json.responsavelMae ? json.responsavelMae : null),
      (json.sexo ? json.sexo : ''),
      (json.dataNascimento ? this.localizationService.getDataFormatoPtbr(json.dataNascimento) : ''),
      json.email,
      (json.telefone ? json.telefone : null),
      (json.cep ? json.cep : null),
      (json.endereco ? json.endereco : null),
      (json.numero ? json.numero : null),
      (json.bairro ? json.bairro : null),
      (json.complemento ? json.complemento : null),
      (json.cpfMae ? json.cpfMae : null),
      (json.cpfPai ? json.cpfPai : null),
      (json.remetenteConviteMatricula ? json.remetenteConviteMatricula : null),
      (json.rg ? json.rg : null),
      (json.codigoEvento ? json.codigoEvento : null),
      json.usuarioResponsavel,
      json.codigoRegistroAcessoPagina,
      (json.freepass ? json.freepass : null),
      this.aulasService.codigosAulasSelecionadas(),
      false,
      null
      );
  }

  concluir(voucher?: any): void {
    const title = this.translateService.instant('checkout.cadastro-realizado-com-sucesso');
    let htmlDescription = '';
    if (voucher && voucher !== '') {
      htmlDescription = `${this.translateService.instant('checkout.seu-voucher')} ${voucher} </br>`;
    }
    htmlDescription += `${this.translateService.instant('checkout.parabens-bom-treino')}`;
    Swal.fire({
      type: 'success',
      title: title,
      html: htmlDescription,
      showConfirmButton: true,
      onClose: () => {
        window.localStorage.removeItem('cliente');
        this.redirectPosVenda();
      }
    });
  }
  redirectPosVenda(): void {
    Swal.fire({
      onOpen: function () {
        Swal.showLoading();
      }
    });
    window.location.href = this.urlRedirect();
  }
  urlRedirect(): string {
    window.localStorage.setItem('msgFinalOperacao', '4');
    if (this.empresaService.config === undefined ||
      ((!(this.empresaService.config === undefined)) &&
        this.empresaService.config != null &&
        this.empresaService.config.url != null &&
        this.empresaService.config.url === '')) {
      var url = window.location.href;
      url = url.substring(0, url.indexOf('agenda-aulas'));
      url = url + 'pospagamento?un=' + this.negociacaoService.codunidade + '&k=' + this.negociacaoService.chave;
      return url;
    }
    return this.empresaService.config.url;
  }
  RealizaodAprovada(msg: string): void {
    const title = this.translateService.instant('checkout.aluno-nao-foi-adicionado');
    const text =  this.translateService.instant('checkout.cadastro-nao-realizado-revisar-informacoes') + msg;
    Swal.fire({
      type: 'error',
      title: title,
      text: text,
      showConfirmButton: true
    });
  }
}
