FROM node:10-alpine as builder

RUN apk add --no-cache git

WORKDIR /app
COPY . .
RUN npm install
RUN npm rebuild node-sass
RUN npm install --save moment
RUN sh ./scripts/build-spa.sh -m 'prod' -u '/' -a 'https://app.pactosolucoes.com.br/api/prest'
RUN ls /app

FROM nginx:alpine

RUN apk add --update nodejs npm

ENV URL_API=${URL_API:-"https://app.pactosolucoes.com.br/api/prest"}

COPY docker/conf/nginx.conf /etc/nginx/nginx.conf

WORKDIR /usr/share/nginx/html
COPY --from=builder /app/dist/ .
COPY docker/bin /bin
RUN cd /bin && npm install
COPY docker/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

EXPOSE 80
ENTRYPOINT [ "sh", "/entrypoint.sh" ]
